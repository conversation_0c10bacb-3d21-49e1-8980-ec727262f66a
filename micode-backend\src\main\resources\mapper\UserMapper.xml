<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yanweiyi.micodebackend.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.yanweiyi.micodebackend.model.entity.User">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="avatarUrl" column="avatarUrl" jdbcType="VARCHAR"/>
        <result property="userProfile" column="userProfile" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="TINYINT"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="userRole" column="userRole" jdbcType="VARCHAR"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="isDelete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,username,password,
        avatarUrl,userProfile,gender,
        email,userRole,
        createTime,updateTime,isDelete
    </sql>
</mapper>
