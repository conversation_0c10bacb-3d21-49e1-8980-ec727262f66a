<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yanweiyi.micodebackend.mapper.QuestionSubmitMapper">

    <resultMap id="BaseResultMap" type="com.yanweiyi.micodebackend.model.entity.QuestionSubmit">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="language" column="language" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="judgeInfo" column="judgeInfo" jdbcType="VARCHAR"/>
        <result property="questionId" column="questionId" jdbcType="BIGINT"/>
        <result property="userId" column="userId" jdbcType="BIGINT"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="isDelete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,language,code,
        status,judgeInfo,questionId,
        userId,createTime,updateTime,
        isDelete
    </sql>

    <select id="selectLastQuestionSubmitList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM question_submit
        WHERE id IN (
        SELECT MAX(id) AS id
        FROM question_submit
        WHERE userId = #{userId}
        GROUP BY questionId
        )
    </select>
</mapper>
