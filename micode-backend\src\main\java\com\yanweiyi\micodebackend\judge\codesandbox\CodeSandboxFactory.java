package com.yanweiyi.micodebackend.judge.codesandbox;

import com.yanweiyi.micodebackend.judge.codesandbox.impl.RemoteCodeSandbox;
import com.yanweiyi.micodebackend.judge.codesandbox.impl.ThirdPartyCodeSandbox;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 代码沙箱工厂（根据字符串创建指定的代码沙箱实例）
 *
 * <AUTHOR>
 */
@Component
public class CodeSandboxFactory {

    @Resource
    private RemoteCodeSandbox remoteCodeSandbox;

    @Resource
    private ThirdPartyCodeSandbox thirdPartyCodeSandbox;

    /**
     * 获取代码沙箱对象（使用了工厂模式）
     */
    public CodeSandbox newInstance(String type) {
        switch (type) {
            case "remote":
                return remoteCodeSandbox;
            case "thirdParty":
                return thirdPartyCodeSandbox;
            default:
                throw new IllegalArgumentException("Unable to find code sandbox named '" + type + "'");
        }
    }

}
