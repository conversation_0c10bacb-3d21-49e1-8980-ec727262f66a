<template>
  <div class="leaderboard-container">
    <h2 class="leaderboard-title">排行榜</h2>
    <template v-if="rankings">
      <div class="rank-selector">
        <el-form label-width="auto" inline>
          <el-form-item label="显示排名前" style="width: 220px">
            <el-select v-model="limit" placeholder="请选择" @change="loadData">
              <el-option label="20" :value="20" />
              <el-option label="50" :value="50" />
              <el-option label="100" :value="100" />
            </el-select>
          </el-form-item>
          <el-form-item label="题目难度" style="width: 220px">
            <el-select
              v-model="difficulty"
              placeholder="请选择"
              @change="loadData"
            >
              <el-option label="全部" value="全部" />
              <el-option label="简单" value="简单" />
              <el-option label="中等" value="中等" />
              <el-option label="困难" value="困难" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="rankings" style="width: 100%" empty-text="暂无数据">
        <!-- 排名列，只显示数字 -->
        <el-table-column prop="rank" label="排名" width="80" align="center">
          <template #default="scope">
            <span>{{ scope.row.rank }}</span>
          </template>
        </el-table-column>

        <!-- 用户名列，头像在用户名左侧 -->
        <el-table-column prop="username" label="用户">
          <template #default="scope">
            <div class="user-info">
              <UserAvatar
                :username="scope.row.username"
                :avatar-url="scope.row.avatarUrl"
                size="small"
                class="user-avatar"
              />
              <span class="user-name">{{ scope.row.username }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="totalSubmissions" label="提交总数">
          <template #default="scope">
            {{ scope.row.totalSubmissions }}
          </template>
        </el-table-column>

        <el-table-column
          prop="acceptedSubmissions"
          label="通过数"
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.acceptedSubmissions }}
          </template>
        </el-table-column>

        <!-- 通过率列 -->
        <el-table-column prop="acceptanceRate" label="通过率">
          <template #default="scope">
            <el-progress :percentage="scope.row.acceptanceRate" />
          </template>
        </el-table-column>
      </el-table>
    </template>
    <div v-else class="no-data">暂无数据</div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { QuestionSubmitControllerService } from "../../openapi";
import UserAvatar from "@/components/UserAvatar.vue";

const rankings = ref([]);
const limit = ref(20);
const difficulty = ref("全部");

const loadData = async () => {
  const res = await QuestionSubmitControllerService.getTopRankedUsersUsingPost(
    difficulty.value,
    limit.value
  );
  rankings.value = res.data;
};

// 在组件挂载时获取排行榜数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.leaderboard-container {
  padding: 6px 20px 20px 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.leaderboard-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

.rank-selector {
  display: flex;
  justify-content: center;
  align-content: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  margin-right: 10px;
}

.user-name {
  cursor: pointer;
  color: #409eff;
  transition: color 0.3s;
}

.user-name:hover {
  color: #66b1ff;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 10vh;
}
</style>
