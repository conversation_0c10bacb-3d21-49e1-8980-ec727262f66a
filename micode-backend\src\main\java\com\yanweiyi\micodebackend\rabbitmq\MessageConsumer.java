package com.yanweiyi.micodebackend.rabbitmq;

import com.rabbitmq.client.Channel;
import com.yanweiyi.micodebackend.common.ApiStatusCode;
import com.yanweiyi.micodebackend.exception.BusinessException;
import com.yanweiyi.micodebackend.judge.service.JudgeService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MessageConsumer {

    @Resource
    private JudgeService judgeService;

    @SneakyThrows
    @RabbitListener(queues = {"judge_queue"}, ackMode = "MANUAL")
    public void receiveMessage(long message, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("receive message: {}", message);
        if (message <= 0) {
            log.error("abnormal questionSubmitId={}", message);
            channel.basicNack(deliveryTag, false, false);
        } else {
            try {
                judgeService.doJudge(message);
                channel.basicAck(deliveryTag, false);
            } catch (Exception e) {
                log.error("questionSubmitId={} judge error: {}", message, e.getMessage());
                throw new BusinessException(ApiStatusCode.SYSTEM_ERROR);
            }
        }
    }
}