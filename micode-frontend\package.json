{"name": "micode-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@bytemd/plugin-breaks": "^1.21.0", "@bytemd/plugin-frontmatter": "^1.21.0", "@bytemd/plugin-gemoji": "^1.21.0", "@bytemd/plugin-gfm": "^1.21.0", "@bytemd/plugin-highlight": "^1.21.0", "@bytemd/plugin-math": "^1.21.0", "@bytemd/plugin-medium-zoom": "^1.21.0", "@bytemd/vue-next": "^1.21.0", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.2", "bytemd": "^1.21.0", "core-js": "^3.8.3", "element-plus": "^2.7.3", "juejin-markdown-themes": "^1.34.0", "moment": "^2.30.1", "monaco-editor": "^0.48.0", "monaco-editor-webpack-plugin": "^7.1.0", "pinia": "^2.1.7", "pinia-plugin-persist": "^1.0.0", "vue": "^3.2.13", "vue-router": "^4.0.3"}, "devDependencies": {"@babel/plugin-transform-class-static-block": "^7.24.6", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "openapi-typescript-codegen": "^0.29.0", "typescript": "~4.5.5"}}