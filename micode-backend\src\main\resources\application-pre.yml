spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ******************************************************
    username: yanweiyi
    password: QUmPCn8vISxCoOY2
  redis:
    database: 0
    host: redis-13581.c281.us-east-1-2.ec2.redns.redis-cloud.com
    port: 13581
    password: MdecI02N9ksWMoEJPif4qrQaFaDSGO2B
  rabbitmq:
    host: fuji-01.lmq.cloudamqp.com
    port: 5672
    username: fqddcfny
    password: CkwX_KaHKPM9YXycCaqG9EeVRzTADix-
    virtual-host: fqddcfny
knife4j:
  enable: false
