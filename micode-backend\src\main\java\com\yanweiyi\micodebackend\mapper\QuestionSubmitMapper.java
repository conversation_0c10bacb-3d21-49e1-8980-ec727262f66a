package com.yanweiyi.micodebackend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yanweiyi.micodebackend.model.entity.QuestionSubmit;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【question_submit(题目提交表)】的数据库操作Mapper
 * @createDate 2024-05-30 21:59:43
 * @Entity com.yanweiyi.micodebackend.model.entity.QuestionSubmit
 */
public interface QuestionSubmitMapper extends BaseMapper<QuestionSubmit> {

    List<QuestionSubmit> selectLastQuestionSubmitList(@Param("userId") long userId);

}





