<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yanweiyi.micodebackend.mapper.PostMapper">
    <resultMap id="BaseResultMap" type="com.yanweiyi.micodebackend.model.entity.Question">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="thumbNum" column="thumbNum" jdbcType="INTEGER"/>
        <result property="userId" column="userId" jdbcType="INTEGER"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="isDelete" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,content,
        thumbNum,userId,createTime,
        updateTime,isDelete
    </sql>

</mapper>

