server:
  port: 11091
  servlet:
    context-path: /api
    session:
      cookie:
        max-age: 2592000 # <PERSON>ie 30 天过期
        path: /api
spring:
  application:
    name: micode-backend
  profiles:
    active: dev
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher # 支持 swagger 3
  session:
    store-type: redis
    timeout: 2592000 # Session 30 天过期
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ***********************************************
    username: root
    password: root
  redis:
    database: 0
    host: localhost
    port: 6379
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # MyBatis 输出日志
knife4j:
  enable: true
  openapi:
    title: "MiCoder 接口文档"
    version: 1.0
    group:
      default:
        api-rule: package
        api-rule-resources:
          - com.yanweiyi.micodebackend.controller
# 代码沙箱配置
codesandbox:
  type: remote
  remote:
    url: http://*************:11090/executeCode
