package com.yanweiyi.micodebackend.judge.strategy.impl;

import com.yanweiyi.micodebackend.judge.model.dto.JudgeInfo;
import com.yanweiyi.micodebackend.judge.strategy.JudgeStrategy;
import com.yanweiyi.micodebackend.judge.strategy.model.JudgeContext;

/**
 * Python 判题策略
 *
 * <AUTHOR>
 */
public class PythonJudgeStrategy implements JudgeStrategy {

    @Override
    public JudgeInfo applyJudgeStrategy(JudgeContext judgeContext) {
        return null;
    }

}
