package com.yanweiyi.micodecodesandbox.sandbox.docker.impl;

import com.yanweiyi.micodecodesandbox.model.ExecuteCodeRequest;
import com.yanweiyi.micodecodesandbox.model.ExecuteCodeResponse;
import com.yanweiyi.micodecodesandbox.sandbox.docker.DockerCodeSandbox;

/**
 * <AUTHOR>
 */
public class PythonDockerCodeSandbox implements DockerCodeSandbox {
    @Override
    public ExecuteCodeResponse executeCode(ExecuteCodeRequest executeCodeRequest) {
        return null;
    }
}
