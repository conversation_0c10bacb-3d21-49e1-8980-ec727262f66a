<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yanweiyi.micodebackend.mapper.QuestionTagMapper">

    <resultMap id="BaseResultMap" type="com.yanweiyi.micodebackend.model.entity.QuestionTag">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tagName" column="tagName" jdbcType="VARCHAR"/>
            <result property="userId" column="userId" jdbcType="BIGINT"/>
            <result property="parentId" column="parentId" jdbcType="BIGINT"/>
            <result property="isParent" column="isParent" jdbcType="TINYINT"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="idDelete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tagName,userId,
        parentId,isParent,isDelete,
        createTime,updateTime
    </sql>
</mapper>
