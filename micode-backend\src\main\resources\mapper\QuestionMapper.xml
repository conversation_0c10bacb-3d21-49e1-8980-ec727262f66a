<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yanweiyi.micodebackend.mapper.QuestionMapper">

    <resultMap id="BaseResultMap" type="com.yanweiyi.micodebackend.model.entity.Question">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="tags" column="tags" jdbcType="VARCHAR"/>
        <result property="answer" column="answer" jdbcType="VARCHAR"/>
        <result property="difficulty" column="difficulty" jdbcType="INTEGER"/>
        <result property="submitNum" column="submitNum" jdbcType="INTEGER"/>
        <result property="acceptedNum" column="acceptedNum" jdbcType="INTEGER"/>
        <result property="judgeCase" column="judgeCase" jdbcType="VARCHAR"/>
        <result property="judgeConfig" column="judgeConfig" jdbcType="VARCHAR"/>
        <result property="userId" column="userId" jdbcType="BIGINT"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="isDelete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,content,
        tags,answer,difficulty,
        submitNum,acceptedNum,judgeCase,
        judgeConfig,userId,createTime,
        updateTime,isDelete
    </sql>

</mapper>
