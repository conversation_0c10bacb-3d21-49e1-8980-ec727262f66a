package com.yanweiyi.micodebackend.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserRankingVO extends UserVO {

    /**
     * 排名
     */
    private int rank;

    /**
     * 提交总数
     */
    private int totalSubmissions;

    /**
     * 通过数
     */
    private int acceptedSubmissions;

    /**
     * 通过率
     */
    private int acceptanceRate;

    /**
     * 增加总提交数
     */
    public void incrementTotalSubmissions() {
        this.totalSubmissions++;
    }

    /**
     * 增加通过数
     */
    public void incrementAcceptedSubmissions() {
        this.acceptedSubmissions++;
    }
}