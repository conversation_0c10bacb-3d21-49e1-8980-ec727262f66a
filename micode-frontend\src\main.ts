import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "bytemd/dist/index.css";
import { createPinia } from "pinia";
import piniaPersist from "pinia-plugin-persist";
import "@/utils/style.css";

const pinia = createPinia();
pinia.use(piniaPersist);

createApp(App).use(ElementPlus).use(pinia).use(router).mount("#app");

router.beforeEach((to, from, next) => {
  const title = to.name;
  if (title && typeof title === "string") {
    document.title = `MiCode - ${title}`;
  }
  next();
});
