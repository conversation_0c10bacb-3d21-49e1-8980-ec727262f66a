<script setup lang="ts">
import { computed, defineProps } from "vue";

const props = defineProps({
  username: {
    type: String,
    required: true,
  },
  size: {
    type: String,
    default: "default", // 可选值: small, default, large
  },
  avatarUrl: {
    type: String,
    default: "",
  },
  customColor: {
    type: String,
    default: "",
  },
});

// 从用户名获取首字母（如果用户名为空或不存在，则使用默认值）
const firstLetter = computed(() => {
  if (!props.username || props.username.length === 0) {
    return "U";
  }
  return props.username.charAt(0).toUpperCase();
});

// 根据用户名生成背景颜色
const backgroundColor = computed(() => {
  if (props.customColor) {
    return props.customColor;
  }

  if (!props.username || props.username.length === 0) {
    return "#409EFF"; // 默认蓝色
  }

  // 使用用户名的字符编码生成颜色
  const hash = props.username.split("").reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);

  // 基于哈希生成HSL颜色，保持饱和度和亮度适中，只变化色相
  const h = Math.abs(hash % 360);
  const s = 70; // 饱和度固定为70%
  const l = 60; // 亮度固定为60%

  return `hsl(${h}, ${s}%, ${l}%)`;
});

// 根据背景颜色决定文字颜色（深色背景用白色文字，浅色背景用黑色文字）
const textColor = computed(() => {
  // 如果提供了自定义颜色，简单返回白色
  if (props.customColor) {
    return "#FFFFFF";
  }
  return "#FFFFFF"; // 默认使用白色文字
});

// 根据size计算尺寸
const sizeStyle = computed(() => {
  switch (props.size) {
    case "small":
      return { width: "32px", height: "32px", fontSize: "14px" };
    case "large":
      return { width: "56px", height: "56px", fontSize: "24px" };
    default:
      return { width: "40px", height: "40px", fontSize: "18px" };
  }
});
</script>

<template>
  <!-- 如果提供了头像URL且有效，则显示图片头像 -->
  <el-avatar
    v-if="avatarUrl"
    :src="avatarUrl"
    :size="size"
    class="avatar-image"
  />

  <!-- 否则显示首字母头像 -->
  <div
    v-else
    class="letter-avatar"
    :style="{
      ...sizeStyle,
      backgroundColor: backgroundColor,
      color: textColor,
    }"
  >
    {{ firstLetter }}
  </div>
</template>

<style scoped>
.letter-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-weight: 500;
  user-select: none;
}

.avatar-image {
  display: block;
}
</style>
